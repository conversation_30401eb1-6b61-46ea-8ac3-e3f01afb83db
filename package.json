{"name": "id-provider", "version": "1.0.0", "description": "身份提供商 (IdP) - 统一身份认证和授权服务", "main": "dist/index.js", "scripts": {"dev": "nodemon -r tsconfig-paths/register --transpile-only src/index.ts", "dev:frontend": "vite", "build": "tsc", "build:frontend": "vite build", "build:all": "npm run build && npm run build:frontend", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:report": "npm run test:coverage && ts-node scripts/test-coverage.ts", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:ci": "jest --coverage --ci --watchAll=false --passWithNoTests", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["identity-provider", "authentication", "authorization", "sso", "oauth2", "oidc", "saml"], "author": "Developer", "license": "MIT", "dependencies": {"@ant-design/icons": "^6.0.0", "@node-saml/node-saml": "^5.1.0", "@prisma/client": "^6.12.0", "@types/ioredis": "^4.28.10", "antd": "^5.26.7", "axios": "^1.11.0", "bcrypt": "^5.1.1", "compression": "^1.8.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-slow-down": "^3.0.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.7.0", "joi": "^17.11.0", "jose": "^6.0.13", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "node-forge": "^1.3.1", "nodemailer": "^6.9.7", "oidc-provider": "^9.4.2", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prom-client": "^15.1.3", "qrcode": "^1.5.3", "qrcode.react": "^4.2.0", "ra-data-simple-rest": "^5.10.2", "react": "^19.1.1", "react-admin": "^5.10.2", "react-dom": "^19.1.1", "react-router-dom": "^7.7.1", "saml2-js": "^4.0.4", "speakeasy": "^2.0.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1", "zod": "^4.0.17", "zustand": "^5.0.7"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.8.1", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/json2csv": "^5.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.4", "@types/node-forge": "^1.3.13", "@types/nodemailer": "^6.4.14", "@types/passport": "^1.0.16", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth20": "^2.0.14", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/speakeasy": "^2.0.10", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-react": "^4.7.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.12.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "vite": "^7.0.6"}, "engines": {"node": ">=18.0.0"}}